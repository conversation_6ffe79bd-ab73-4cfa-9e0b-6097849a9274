<?php
header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
    exit;
}

require_once '../apiExample/bento.php';

// ========== MONITORING FUNCTIONS ==========
/**
 * Send debug data to Pipedream for real-time monitoring - NON-BLOCKING
 */
function sendPipedreamLog($stage, $data, $context = []) {
    try {
        $debugData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'stage' => $stage,
            'service' => 'HubSpotIntegration',
            'environment' => $_SERVER['HTTP_HOST'] ?? 'unknown',
            'context' => $context,
            'data' => $data
        ];

        $curl = curl_init("https://eo8vqnch968fhpz.m.pipedream.net");
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query([
            'hubspot_debug' => json_encode($debugData, JSON_PRETTY_PRINT)
        ]));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 2); // Reduced timeout
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 1); // Connection timeout
        curl_setopt($curl, CURLOPT_FAILONERROR, false); // Don't fail on HTTP errors
        curl_exec($curl);
        curl_close($curl);
    } catch (Exception $e) {
        // Silently fail - monitoring should never block the integration
    }
}



// Parse HubSpot payload
$json = file_get_contents("php://input");
$data = json_decode($json, true);

// 🔥 LOG WEBHOOK RECEIVED 🔥
sendPipedreamLog('WEBHOOK_RECEIVED', [
    'payload_size' => strlen($json),
    'parsed_data' => $data,
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
]);

// Environment configuration function
function getEnvironmentConfig() {
    $host = $_SERVER['HTTP_HOST'];

    if ($host === 'localhost:8080') {
        return [
            'instance' => 'rickyvoltz',
            'api_token' => '1ac04c9bfe10b64d1fa9cdff6216035d9795e2a1f46e3fb5df36d6c9a2ec1169fea0618fbc014a0ff8381a75974f1bb46c6e2c1dd9c40ff4db6594a122d9d5ca',
            'company_type_id' => 24,
            'contact_type_id' => 23,
            'email_contact_info_type_id' => 14,
            'lead_source_contact_info_type_id' => 17,
            'hq_id' => 3530,
            'hubspot_managers' => [
                '79905155' => 20495730,
                '79618815' => 20495729,
                '79905153' => 20495728
            ],
            'default_manager' => 20495729
        ];
    } else {
        return [
            'instance' => 'infinity',
            'api_token' => '93cfbc982047ca935cd83883bed1fb9f08580cfec6db37beda0e91d7353d2508fe587baa3275d8747bf5f792c72825b3a4f12365926c08be3e1aef7425980060',
            'company_type_id' => 6004528,
            'contact_type_id' => 1929600,
            'email_contact_info_type_id' => 1119285,
            'lead_source_contact_info_type_id' => 1828172,
            'hq_id' => 1476971,
            'hubspot_managers' => [
                '79905155' => 5461358,
                '79618815' => 20295118,
                '79905153' => 17220899
            ],
            'default_manager' => 20295118
        ];
    }
}

// Manager resolution function
function resolveManager($hubspot_owner_id, $config) {
    if (!empty($hubspot_owner_id) && isset($config['hubspot_managers'][$hubspot_owner_id])) {
        return $config['hubspot_managers'][$hubspot_owner_id];
    }
    return $config['default_manager'];
}

// Environment configuration
$config = getEnvironmentConfig();
$bento = new BentoAPI($config['instance'], $config['api_token']);

// Extract data from HubSpot payload
$firstname = $data['firstname'] ?? '';
$lastname = $data['lastname'] ?? '';
$email = $data['email'] ?? '';
$dealname = $data['dealname'] ?? '';
$lead_source = $data['lead_source'] ?? '';
$hubspot_owner_id = $data['hubspot_owner_id'] ?? '';



// Resolve manager
$manager_id = resolveManager($hubspot_owner_id, $config);

// 🔥 LOG PAYLOAD PROCESSING 🔥
sendPipedreamLog('PAYLOAD_PROCESSED', [
    'contact_name' => $firstname . ' ' . $lastname,
    'email' => $email,
    'company' => $dealname,
    'lead_source' => $lead_source,
    'hubspot_owner_id' => $hubspot_owner_id,
    'resolved_manager_id' => $manager_id,
    'environment' => $config['instance']
]);

// ========== COMPANY OPERATIONS (CONDITIONAL) ==========
$company = null;

// Only process company if dealname is provided
if (!empty(trim($dealname))) {
    // Normalize company name for better matching
    $normalized_dealname = trim($dealname);

    // 🔥 LOG COMPANY SEARCH 🔥
    sendPipedreamLog('COMPANY_SEARCH_START', [
        'original_name' => $dealname,
        'normalized_name' => $normalized_dealname
    ]);

    // First try exact match
    $existing_company = $bento->getWhere([
        'objectType' => 'companies',
        'queryObj' => ['name' => $normalized_dealname]
    ]);

    // If no exact match, search all companies and do case-insensitive comparison
    if (empty($existing_company)) {
        // Get all companies with similar type and parent to narrow search
        $all_companies = $bento->getWhere([
            'objectType' => 'companies',
            'queryObj' => [
                'type' => $config['company_type_id'],
                'parent' => $config['hq_id']
            ]
        ]);

        // Case-insensitive search through results
        $normalized_search = strtolower($normalized_dealname);
        foreach ($all_companies as $company_candidate) {
            if (strtolower(trim($company_candidate['name'])) === $normalized_search) {
                $existing_company = [$company_candidate];
                sendPipedreamLog('COMPANY_CASE_INSENSITIVE_MATCH', [
                    'search_name' => $dealname,
                    'found_name' => $company_candidate['name'],
                    'company_id' => $company_candidate['id']
                ]);
                break;
            }
        }
    }

    // 🔥 LOG SEARCH RESULTS 🔥
    sendPipedreamLog('COMPANY_SEARCH_RESULTS', [
        'results_count' => count($existing_company),
        'results' => array_map(function($comp) {
            return ['id' => $comp['id'], 'name' => $comp['name']];
        }, $existing_company)
    ]);

    if (!empty($existing_company)) {
        // Use the first matching company
        $company = $existing_company[0];

        // 🔥 LOG EXISTING COMPANY FOUND 🔥
        sendPipedreamLog('COMPANY_FOUND', [
            'company_id' => $company['id'],
            'company_name' => $company['name'],
            'matched_from' => $normalized_dealname,
            'total_matches' => count($existing_company)
        ]);

        // If multiple matches found, log warning
        if (count($existing_company) > 1) {
            sendPipedreamLog('COMPANY_MULTIPLE_MATCHES', [
                'search_name' => $normalized_dealname,
                'matches_count' => count($existing_company),
                'selected_company' => ['id' => $company['id'], 'name' => $company['name']],
                'all_matches' => array_map(function($comp) {
                    return ['id' => $comp['id'], 'name' => $comp['name']];
                }, $existing_company)
            ]);
        }
    } else {
        $company = $bento->create([
            'objectType' => 'companies',
            'objectData' => [
                'type' => $config['company_type_id'],
                'name' => $normalized_dealname,
                'parent' => $config['hq_id'],
            'tagged_with' => [$config['hq_id']],
            'data_source' => crc32('hubspot'),
            'data_source_id' => crc32($normalized_dealname)
        ]
    ]);

        // 🔥 LOG NEW COMPANY CREATED 🔥
        sendPipedreamLog('COMPANY_CREATED', [
            'company_id' => $company['id'] ?? null,
            'company_name' => $normalized_dealname,
            'original_name' => $dealname
        ]);
    }
} else {
    // No company name provided - skip company creation
    sendPipedreamLog('COMPANY_SKIPPED', [
        'reason' => 'No company/deal name provided',
        'dealname_raw' => $dealname
    ]);
}

// ========== ENHANCED CONTACT DEDUPLICATION ==========
// Normalize email for better matching
$normalized_email = trim(strtolower($email));

// 🔥 LOG CONTACT SEARCH 🔥
sendPipedreamLog('CONTACT_SEARCH_START', [
    'original_email' => $email,
    'normalized_email' => $normalized_email,
    'search_criteria' => [
        'info' => $normalized_email,
        'type' => $config['email_contact_info_type_id']
    ]
]);

$email_contact_info_search = $bento->getWhere([
    'objectType' => 'contact_info',
    'queryObj' => [
        'info' => $normalized_email,
        'type' => $config['email_contact_info_type_id']
    ]
]);

// 🔥 LOG EMAIL SEARCH RESULTS 🔥
sendPipedreamLog('CONTACT_EMAIL_SEARCH_RESULTS', [
    'email_records_found' => count($email_contact_info_search),
    'email_records' => array_map(function($info) {
        return ['id' => $info['id'], 'object_id' => $info['object_id'], 'info' => $info['info']];
    }, $email_contact_info_search)
]);

$existing_contact = [];
$existing_email_contact_info = null;

if (!empty($email_contact_info_search)) {
    // Loop through all contact_info records to find the first valid contact
    foreach ($email_contact_info_search as $contact_info) {
        $contact_id = $contact_info['object_id'];

        if ($contact_id > 0) {
            $contact_lookup = $bento->getById($contact_id);
            if (!empty($contact_lookup)) {
                $existing_contact = [$contact_lookup];
                $existing_email_contact_info = $contact_info;

                // 🔥 LOG VALID CONTACT FOUND 🔥
                sendPipedreamLog('CONTACT_FOUND_VIA_EMAIL', [
                    'contact_id' => $contact_id,
                    'contact_name' => $contact_lookup['name'] ?? 'Unknown',
                    'email_contact_info_id' => $contact_info['id'],
                    'matched_email' => $normalized_email
                ]);

                break; // Found valid contact, stop looking
            }
        }
    }
}

$is_new_contact = false;

if (!empty($existing_contact)) {
    $contact = $existing_contact[0];

    // Build contact update data
    $contact_update_data = [
        'id' => $contact['id'],
        'fname' => $firstname,
        'lname' => $lastname,
        'name' => $firstname . ' ' . $lastname,
        'manager' => $manager_id,
        'lead_source' => $lead_source,
        'type' => $config['contact_type_id'],
        'state' => 1
    ];

    // Add company info only if company exists
    $tagged_with = [$config['hq_id'], $manager_id];
    if ($company) {
        $contact_update_data['company'] = $company['id'];
        $contact_update_data['parent'] = $company['id'];
        $tagged_with[] = $company['id'];
    }
    $contact_update_data['tagged_with'] = $tagged_with;

    $bento->update([
        'objectType' => 'contacts',
        'objectData' => $contact_update_data
    ]);

    // 🔥 LOG EXISTING CONTACT UPDATED 🔥
    sendPipedreamLog('CONTACT_UPDATED', [
        'contact_id' => $contact['id'],
        'contact_name' => $firstname . ' ' . $lastname,
        'email' => $email,
        'company_id' => $company['id'] ?? null
    ]);
} else {
    // Build contact creation data
    $contact_create_data = [
        'type' => $config['contact_type_id'],
        'name' => $firstname . ' ' . $lastname,
        'fname' => $firstname,
        'lname' => $lastname,
        'manager' => $manager_id,
        'lead_source' => $lead_source,
        'data_source_hash' => $email,
        'data_source' => crc32('hubspot'),
        'data_source_id' => intval($hubspot_owner_id)
    ];

    // Add company info only if company exists
    $tagged_with = [$config['hq_id'], $manager_id];
    if ($company) {
        $contact_create_data['company'] = $company['id'];
        $contact_create_data['parent'] = $company['id'];
        $tagged_with[] = $company['id'];
    }
    $contact_create_data['tagged_with'] = $tagged_with;

    $contact = $bento->create([
        'objectType' => 'contacts',
        'objectData' => $contact_create_data
    ]);
    $is_new_contact = true;
    // 🔥 LOG NEW CONTACT CREATED 🔥
    sendPipedreamLog('CONTACT_CREATED', [
        'contact_id' => $contact['id'] ?? null,
        'contact_name' => $firstname . ' ' . $lastname,
        'email' => $email,
        'company_id' => $company['id'] ?? null,
        'lead_source' => $lead_source,
        'manager_id' => $manager_id
    ]);


}

// ========== EMAIL CONTACT_INFO OPERATIONS ==========
$email_contact_info_was_created = false;

if (!empty($existing_email_contact_info)) {
    $email_contact_info = $existing_email_contact_info;
} else {
    $email_contact_info = $bento->create([
        'objectType' => 'contact_info',
        'objectData' => [
            'object_id' => $contact['id'],
            'object_type' => 'contacts',
            'name' => 'Email Address',
            'title' => 'Email Address',
            'info' => $email,
            'type' => $config['email_contact_info_type_id'],
            'is_primary' => 'yes',
            'data_source' => crc32('hubspot'),
            'data_source_id' => intval($hubspot_owner_id)
        ]
    ]);
    $email_contact_info_was_created = true;
}

// ========== LEAD SOURCE CONTACT_INFO OPERATIONS ==========
$lead_source_contact_info = null;
$lead_source_contact_info_was_created = false;

if (!empty($lead_source)) {
    $existing_lead_source_info = $bento->getWhere([
        'objectType' => 'contact_info',
        'queryObj' => [
            'object_id' => $contact['id'],
            'info' => $lead_source,
            'type' => $config['lead_source_contact_info_type_id']
        ]
    ]);

    if (!empty($existing_lead_source_info)) {
        $lead_source_contact_info = $existing_lead_source_info[0];
    } else {
        $lead_source_contact_info = $bento->create([
            'objectType' => 'contact_info',
            'objectData' => [
                'object_id' => $contact['id'],
                'object_type' => 'contacts',
                'name' => 'Lead Source',
                'title' => 'Lead Source',
                'info' => $lead_source,
                'type' => $config['lead_source_contact_info_type_id'],
                'is_primary' => 'yes',
                'data_source' => crc32('hubspot'),
                'data_source_id' => intval($hubspot_owner_id)
            ]
        ]);
        $lead_source_contact_info_was_created = true;
    }
}

// ========== CONTACT_INFO LINKING OPERATIONS ==========
$new_contact_info_ids = [];

if ($email_contact_info_was_created) {
    $new_contact_info_ids[] = $email_contact_info['id'];
}

if ($lead_source_contact_info && $lead_source_contact_info_was_created) {
    $new_contact_info_ids[] = $lead_source_contact_info['id'];
}

if (!empty($new_contact_info_ids)) {
    $existing_contact_info = $contact['contact_info'] ?? [];
    $all_contact_info_ids = array_unique(array_merge($existing_contact_info, $new_contact_info_ids));

    $bento->update([
        'objectType' => 'contacts',
        'objectData' => [
            'id' => $contact['id'],
            'contact_info' => $all_contact_info_ids
        ]
    ]);
}

// ========== SYSTEM NOTE CREATION ==========
// Get manager name for display
$manager_name = 'Unknown Manager';
$manager_record = $bento->getById($manager_id);
if (!empty($manager_record) && isset($manager_record['name'])) {
    $manager_name = $manager_record['name'];
}

// Create contextual note content matching requested format
$contact_name = $firstname . ' ' . $lastname;
$hubspot_details = '<p><strong>' . $contact_name . '</strong> (' . $email . ') has been added to <strong>' . $dealname . '</strong></p>';
$hubspot_details .= '<p></p>'; // Empty line for spacing

if (!empty($lead_source)) {
    $hubspot_details .= '<p>Lead came from: <strong>' . $lead_source . '</strong></p>';
    $hubspot_details .= '<p></p>'; // Empty line for spacing
}

$hubspot_details .= '<p>Assigned to <strong>' . $manager_name . '</strong> for follow-up</p>';
$hubspot_details .= '<p></p>'; // Empty line for spacing
$hubspot_details .= '<p><em>Processed on ' . date('M j, Y \a\t g:i A') . '</em></p>';

$noteObj = array(
    'type_id' => $contact['id'],
    'icon' => [
        'icon' => 'hubspot',
        'color' => 'orange'
    ],
    'log_type' => 'hubspot-integration',
    'type' => 'contacts',
    'note' => $hubspot_details,
    'record_type' => 'log',
    'author' => 0,
    'notifyUsers' => [],
    'tagged_with' => [$config['hq_id'], $company['id'], $manager_id, $contact['id']],
    'note_type' => 6512957,
    'notification' => true
);

$system_note = $bento->create([
    'objectType' => 'notes',
    'objectData' => $noteObj
]);

// Success response (send BEFORE creating client notes)
$response = [
    'status' => 'success',
    'message' => 'HubSpot webhook processed successfully',
    'timestamp' => date('Y-m-d H:i:s'),
    'created_records' => [
        'company' => $company ? [
            'id' => $company['id'] ?? null,
            'name' => $company['name'] ?? null,
            'type' => $company['type'] ?? null,
            'parent' => $company['parent'] ?? null
        ] : null,
        'contact' => [
            'id' => $contact['id'] ?? null,
            'name' => $contact['name'] ?? null,
            'fname' => $contact['fname'] ?? null,
            'lname' => $contact['lname'] ?? null,
            'company' => $contact['company'] ?? null,
            'manager' => $manager_id ?? null
        ],
        'email_contact_info' => [
            'id' => $email_contact_info['id'] ?? null,
            'object_id' => $email_contact_info['object_id'] ?? null,
            'info' => $email_contact_info['info'] ?? null,
            'type' => $email_contact_info['type'] ?? null
        ],
        'lead_source_contact_info' => [
            'id' => $lead_source_contact_info['id'] ?? null,
            'object_id' => $lead_source_contact_info['object_id'] ?? null,
            'info' => $lead_source_contact_info['info'] ?? null,
            'type' => $lead_source_contact_info['type'] ?? null
        ],
        'system_note' => [
            'id' => $system_note['id'] ?? null,
            'type_id' => $system_note['type_id'] ?? null,
            'log_type' => $system_note['log_type'] ?? null,
            'record_type' => $system_note['record_type'] ?? null,
            'author' => $system_note['author'] ?? null
        ]
    ],
    'environment' => $config['instance']
];

// 🔥 LOG SUCCESSFUL PROCESSING 🔥
sendPipedreamLog('PROCESSING_SUCCESS', [
    'contact_id' => $contact['id'] ?? null,
    'company_id' => $company['id'] ?? null,
    'system_note_id' => $system_note['id'] ?? null,
    'is_new_contact' => $is_new_contact,
    'processing_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
    'environment' => $config['instance']
]);

$json_response = json_encode($response);

// Set headers BEFORE any output
header('Connection: close');
header('Content-Length: ' . strlen($json_response));
http_response_code(200);

// Send response
echo $json_response;

// Flush and close connection
if (ob_get_level()) {
    ob_end_flush();
}
flush();

// Add error handling wrapper for any uncaught exceptions
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        // 🔥 LOG FATAL ERROR + SEND EMAIL ALERT 🔥
        sendPipedreamLog('FATAL_ERROR', [
            'error_message' => $error['message'],
            'error_file' => $error['file'],
            'error_line' => $error['line'],
            'error_type' => $error['type']
        ]);


    }
});

?>
